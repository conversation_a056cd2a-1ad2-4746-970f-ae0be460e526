<template>
  <Card
    class="group hover:shadow-lg transition-all duration-300 cursor-pointer h-full flex flex-col"
  >
    <CardContent class="p-0 flex-1 flex flex-col">
      <!-- Event Image -->
      <div
        class="relative h-40 sm:h-48 bg-gradient-to-br from-primary-100 to-primary-200 rounded-t-lg overflow-hidden"
      >
        <img
          v-if="event.imageUrl"
          :src="getImageUrl(event.imageUrl)"
          :alt="event.title"
          class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          @error="handleImageError"
        />
        <div v-else class="flex items-center justify-center h-full">
          <Icon name="lucide:calendar" class="w-8 h-8 sm:w-12 sm:h-12 text-primary-400" />
        </div>

        <!-- Action Buttons -->
        <div
          class="absolute top-2 sm:top-3 right-2 sm:right-3 flex space-x-1 sm:space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
        >
          <Button
            @click.stop="$emit('edit', event)"
            size="sm"
            variant="outline"
            class="bg-white/90 hover:bg-white border-white/50 shadow-lg"
          >
            <Icon name="lucide:edit" class="w-4 h-4" />
            <span class="sr-only">Edit event</span>
          </Button>
          <Button
            @click.stop="$emit('delete', event)"
            size="sm"
            variant="outline"
            class="bg-white/90 hover:bg-white border-white/50 text-red-600 hover:text-red-700 shadow-lg"
          >
            <Icon name="lucide:trash-2" class="w-4 h-4" />
            <span class="sr-only">Delete event</span>
          </Button>
        </div>

        <!-- Status Badge -->
        <div class="absolute top-3 left-3">
          <span
            :class="[
              'px-2 py-1 text-xs font-medium rounded-full',
              event.isActive
                ? 'bg-green-100 text-green-800'
                : 'bg-gray-100 text-gray-800',
            ]"
          >
            {{ event.isActive ? "Active" : "Inactive" }}
          </span>
        </div>
      </div>

      <!-- Event Details -->
      <div class="p-3 sm:p-4 flex-1 flex flex-col">
        <div class="flex items-start justify-between mb-2 gap-2">
          <h3 class="font-semibold text-base sm:text-lg text-neutral-900 line-clamp-1 flex-1 min-w-0">
            {{ event.title }}
          </h3>
          <div
            v-if="event.price"
            class="text-base sm:text-lg font-bold text-primary-600 whitespace-nowrap"
          >
            {{ formatCurrency(event.price) }}
          </div>
        </div>

        <p class="text-neutral-600 text-xs sm:text-sm mb-3 line-clamp-2 leading-relaxed">
          {{ event.description }}
        </p>

        <!-- Event Info -->
        <div class="space-y-1.5 sm:space-y-2 text-xs sm:text-sm text-neutral-500 flex-1">
          <!-- Date and Time -->
          <div class="flex items-center space-x-1">
            <Icon name="lucide:calendar" class="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
            <span class="truncate">{{ formatEventDate(event.startDate, event.endDate) }}</span>
          </div>

          <div class="flex items-center space-x-1">
            <Icon name="lucide:clock" class="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
            <span class="truncate">{{ event.startTime }} - {{ event.endTime }}</span>
          </div>

          <!-- Location -->
          <div v-if="event.location" class="flex items-center space-x-1">
            <Icon name="lucide:map-pin" class="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
            <span class="truncate">{{ event.location }}</span>
          </div>

          <!-- Attendees -->
          <div v-if="event.maxAttendees" class="flex items-center space-x-1">
            <Icon name="lucide:users" class="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
            <span class="truncate">{{ event.currentAttendees }}/{{ event.maxAttendees }} attendees</span>
          </div>

          <!-- Category -->
          <div v-if="event.category" class="flex items-center space-x-1">
            <Icon name="lucide:tag" class="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" />
            <span class="truncate">{{ event.category }}</span>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import type { Event } from "~/types/service.types";
import { formatCurrency } from "~/utils/services";

interface Props {
  event: Event;
}

interface Emits {
  (e: "edit", event: Event): void;
  (e: "delete", event: Event): void;
}

defineProps<Props>();
defineEmits<Emits>();

// Helper function to format event date
const formatEventDate = (startDate: string, endDate: string): string => {
  const start = new Date(startDate);
  const end = new Date(endDate);

  const startFormatted = start.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });

  // If same day, show only start date
  if (startDate === endDate) {
    return startFormatted;
  }

  const endFormatted = end.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });

  return `${startFormatted} - ${endFormatted}`;
};

// Helper function to get proper image URL
const getImageUrl = (imageUrl: string): string => {
  if (!imageUrl) return "";

  // If it's already a full URL, return as is
  if (
    imageUrl.startsWith("http://") ||
    imageUrl.startsWith("https://") ||
    imageUrl.startsWith("data:")
  ) {
    return imageUrl;
  }

  // If it's a relative URL, prepend the API base URL
  const config = useRuntimeConfig();
  return `${config.public.apiBase}${
    imageUrl.startsWith("/") ? "" : "/"
  }${imageUrl}`;
};

// Handle image loading errors
const handleImageError = (event: any) => {
  const img = event.target as HTMLImageElement;
  console.warn("Failed to load event image:", img.src);
  // Hide the image element so the fallback div shows
  img.style.display = "none";
};
</script>

<style scoped>
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
