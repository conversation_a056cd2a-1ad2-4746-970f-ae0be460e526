<template>
  <Teleport to="body">
    <div class="fixed inset-0 z-[9999] overflow-y-auto">
      <!-- Backdrop -->
      <div class="fixed inset-0 bg-opacity-100 backdrop-blur-sm transition-opacity" @click="$emit('close')"></div>

      <!-- Modal -->
      <div class="flex min-h-full items-center justify-center p-4">
        <div class="relative bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <!-- Header -->
        <div class="flex items-center justify-between p-6 border-b border-neutral-200">
          <h2 class="text-xl font-semibold text-neutral-900">
            {{ service ? 'Edit Service' : 'Add New Service' }}
          </h2>
          <Button
            @click="$emit('close')"
            variant="ghost"
            size="sm"
            class="text-neutral-400 hover:text-neutral-600"
          >
            <Icon name="lucide:x" class="w-5 h-5" />
          </Button>
        </div>

        <!-- Form -->
        <form @submit.prevent="handleSubmit" class="p-6 space-y-6">
          <!-- Image Upload -->
          <div class="space-y-2">
            <label class="block text-sm font-medium text-neutral-700">Service Image</label>
            <ImageUpload
              v-model="form.imageUrl"
              @upload="handleImageUpload"
              :loading="imageUploading"
            />
          </div>

          <!-- Service Name -->
          <div class="space-y-2">
            <label class="block text-sm font-medium text-neutral-700">
              Service Name <span class="text-red-500">*</span>
            </label>
            <input
              v-model="form.name"
              type="text"
              required
              class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Enter service name"
            />
          </div>

          <!-- Description -->
          <div class="space-y-2">
            <label class="block text-sm font-medium text-neutral-700">
              Description <span class="text-red-500">*</span>
            </label>
            <textarea
              v-model="form.description"
              required
              rows="3"
              class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Describe your service"
            ></textarea>
          </div>

          <!-- Price and Duration -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <label class="block text-sm font-medium text-neutral-700">
                Price (₵) <span class="text-red-500">*</span>
              </label>
              <input
                v-model.number="form.price"
                type="number"
                min="0"
                step="0.01"
                required
                class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="0.00"
              />
            </div>
            
            <div class="space-y-2">
              <label class="block text-sm font-medium text-neutral-700">
                Duration (minutes) <span class="text-red-500">*</span>
              </label>
              <input
                v-model.number="form.duration"
                type="number"
                min="1"
                required
                class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="30"
              />
            </div>
          </div>

          <!-- Category -->
          <div class="space-y-2">
            <label class="block text-sm font-medium text-neutral-700">Category</label>
            <input
              v-model="form.category"
              type="text"
              class="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="e.g., Hair Care, Massage, Beauty"
            />
          </div>

          <!-- Service Overlap Toggle -->
          <div class="space-y-3 p-4 bg-neutral-50 rounded-lg border border-neutral-200">
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <label class="block text-sm font-medium text-neutral-700 mb-1">
                  Service Overlap
                </label>
                <p class="text-xs text-neutral-600">
                  Enable multiple clients bookings at the same time
                </p>
              </div>
              <div class="flex items-center space-x-2">
                <Switch
                  v-model:checked="form.allowOverlap"
                  id="allowOverlap"
                />
                <label for="allowOverlap" class="text-sm text-neutral-700">
                  {{ form.allowOverlap ? 'Enabled' : 'Disabled' }}
                </label>
              </div>
            </div>
          </div>

          <!-- Active Status -->
          <div class="flex items-center space-x-2">
            <Switch
              v-model:checked="form.isActive"
              id="isActive"
            />
            <label for="isActive" class="text-sm font-medium text-neutral-700">
              Service is active and bookable
            </label>
          </div>

          <!-- Form Actions -->
          <div class="flex justify-end space-x-3 pt-4 border-t border-neutral-200">
            <Button
              type="button"
              @click="$emit('close')"
              variant="outline"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              :disabled="loading"
              class="bg-primary-600 text-white hover:bg-primary-700"
            >
              <Icon v-if="loading" name="lucide:loader-2" class="w-4 h-4 mr-2 animate-spin" />
              {{ service ? 'Update Service' : 'Create Service' }}
            </Button>
          </div>
        </form>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import type { Service, ServiceFormData } from '~/types/service.types'
import { Switch } from '@/components/ui/switch'
import ImageUpload from '@/components/services/ImageUpload.vue'

interface Props {
  service?: Service | null
}

interface Emits {
  (e: 'close'): void
  (e: 'save', data: ServiceFormData, imageFile?: File | null): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Form state
const loading = ref(false)
const imageUploading = ref(false)
const selectedImageFile = ref<File | null>(null)

const form = reactive<ServiceFormData>({
  name: '',
  description: '',
  price: 0,
  duration: 30,
  imageUrl: '',
  category: '',
  categoryId: '',
  isActive: true, // Services should be defaultly active
  allowOverlap: false // Overlap should be defaultly disabled
})

// Initialize form with service data if editing
watchEffect(() => {
  if (props.service) {
    Object.assign(form, {
      name: props.service.name,
      description: props.service.description,
      price: props.service.price,
      duration: props.service.duration,
      imageUrl: props.service.imageUrl || '',
      category: props.service.category || '',
      categoryId: props.service.categoryId || '',
      isActive: props.service.isActive ?? true,
      allowOverlap: props.service.allowOverlap ?? false
    })
  }
})

// Handle image upload
const handleImageUpload = async (file: File) => {
  selectedImageFile.value = file
  // Store the file for later use when saving the service
  // The actual upload will happen when the form is submitted
}

// Handle form submission
const handleSubmit = async () => {
  loading.value = true
  try {
    // Pass both form data and image file to parent
    emit('save', { ...form }, selectedImageFile.value)
  } finally {
    loading.value = false
  }
}
</script>
