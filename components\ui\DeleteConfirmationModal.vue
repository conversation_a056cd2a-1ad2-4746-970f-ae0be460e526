<template>
  <Teleport to="body">
    <div v-if="show" class="fixed inset-0 z-[9999] overflow-y-auto">
      <!-- Backdrop -->
      <div 
        class="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300"
        @click="$emit('cancel')"
      ></div>

      <!-- Modal -->
      <div class="flex min-h-full items-center justify-center p-4">
        <div 
          class="relative bg-white rounded-xl shadow-xl max-w-md w-full transform transition-all duration-300 scale-100"
          :class="show ? 'animate-in fade-in-0 zoom-in-95' : 'animate-out fade-out-0 zoom-out-95'"
        >
          <!-- Header -->
          <div class="flex items-center justify-center p-6 pb-4">
            <div class="flex items-center justify-center w-12 h-12 bg-red-100 rounded-full">
              <Icon name="lucide:trash-2" class="w-6 h-6 text-red-600" />
            </div>
          </div>

          <!-- Content -->
          <div class="px-6 pb-4 text-center">
            <h3 class="text-lg font-semibold text-neutral-900 mb-2">
              {{ title || `Delete ${itemType}?` }}
            </h3>
            <p class="text-sm text-neutral-600 leading-relaxed">
              {{ message || `Are you sure you want to delete this ${itemType}? This action cannot be undone.` }}
            </p>
            
            <!-- Item Details -->
            <div v-if="itemName" class="mt-4 p-3 bg-neutral-50 rounded-lg border">
              <p class="text-sm font-medium text-neutral-900">{{ itemName }}</p>
              <p v-if="itemDescription" class="text-xs text-neutral-500 mt-1 line-clamp-2">
                {{ itemDescription }}
              </p>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex flex-col sm:flex-row gap-3 p-6 pt-4 border-t border-neutral-200">
            <Button
              @click="$emit('cancel')"
              variant="outline"
              class="flex-1 order-2 sm:order-1"
              :disabled="loading"
            >
              Cancel
            </Button>
            <Button
              @click="$emit('confirm')"
              variant="destructive"
              class="flex-1 order-1 sm:order-2 bg-red-600 text-white hover:bg-red-700 focus:ring-red-500"
              :disabled="loading"
            >
              <Icon v-if="loading" name="lucide:loader-2" class="w-4 h-4 mr-2 animate-spin" />
              {{ confirmText || 'Delete' }}
            </Button>
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
interface Props {
  show: boolean
  title?: string
  message?: string
  itemType: string
  itemName?: string
  itemDescription?: string
  confirmText?: string
  loading?: boolean
}

interface Emits {
  (e: 'confirm'): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// Handle escape key
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && props.show && !props.loading) {
    emit('cancel')
  }
}

// Add/remove event listener
watchEffect(() => {
  if (props.show) {
    document.addEventListener('keydown', handleKeydown)
    document.body.style.overflow = 'hidden'
  } else {
    document.removeEventListener('keydown', handleKeydown)
    document.body.style.overflow = ''
  }
})

// Cleanup on unmount
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  document.body.style.overflow = ''
})
</script>

<style scoped>
@keyframes animate-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes animate-out {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

.animate-in {
  animation: animate-in 0.2s ease-out;
}

.animate-out {
  animation: animate-out 0.15s ease-in;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
