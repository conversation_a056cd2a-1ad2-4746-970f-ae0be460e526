<template>
  <div class="p-4 sm:p-6">
    <!-- Header with Toggle and Add Button -->
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 sm:mb-8 space-y-4 sm:space-y-0">
      <div>
        <h1 class="text-xl sm:text-2xl font-bold text-neutral-900">{{ showEvents ? 'Events' : 'Services' }}</h1>
        <p class="text-sm sm:text-base text-neutral-600 mt-1">{{ showEvents ? 'Manage your events and activities' : 'Manage your services' }}</p>
      </div>
      <div class="flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
        <div class="flex bg-neutral-100 rounded-lg p-1 w-full sm:w-auto">
          <button @click="showEvents = false" :class="toggleButtonClass(false)" class="flex-1 sm:flex-none">
            <Icon name="lucide:briefcase" class="w-4 h-4" />
            <span>Services</span>
          </button>
          <button @click="showEvents = true" :class="toggleButtonClass(true)" class="flex-1 sm:flex-none">
            <Icon name="lucide:calendar" class="w-4 h-4" />
            <span>Events</span>
          </button>
        </div>
        <Button @click="showAddModal = true" class="inline-flex items-center justify-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 w-full sm:w-auto">
          <Icon name="lucide:plus" class="w-4 h-4 mr-2" />
          {{ showEvents ? 'Add Event' : 'Add Service' }}
        </Button>
      </div>
    </div>

    <!-- Search -->
    <div class="mb-6">
      <div class="relative max-w-full sm:max-w-md">
        <Icon name="lucide:search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-4 h-4 sm:w-5 sm:h-5" />
        <input v-model="searchQuery" type="text" :placeholder="`Search ${showEvents ? 'events' : 'services'}...`"
                class="w-full pl-10 pr-4 py-2 sm:py-3 bg-white border border-neutral-200 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm sm:text-base" />
      </div>
    </div>

    <!-- Content Grid -->
    <div v-if="currentItems.length > 0" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
      <ServiceCard v-if="!showEvents" v-for="service in currentItems as Service[]" :key="service.id" :service="service" @edit="editItem" @delete="handleDeleteClick" />
      <EventCard v-else v-for="event in currentItems as Event[]" :key="event.id" :event="event" @edit="editItem" @delete="handleDeleteClick" />
    </div>

    <!-- Empty States -->
    <div v-else class="text-center py-8 sm:py-12">
      <Icon :name="emptyStateIcon" class="w-12 h-12 sm:w-16 sm:h-16 text-neutral-400 mx-auto mb-4" />
      <h3 class="text-base sm:text-lg font-medium text-neutral-900 mb-2">{{ emptyStateTitle }}</h3>
      <p class="text-sm sm:text-base text-neutral-600">{{ emptyStateMessage }}</p>
    </div>

    <!-- Modals -->
    <ServiceModal
      v-if="!showEvents && (showAddModal || editingItem)"
      :service="editingItem as Service"
      @close="closeModal"
      @save="handleSave"
    />
    <EventModal
      v-if="showEvents && (showAddModal || editingItem)"
      :event="editingItem as Event"
      @close="closeModal"
      @save="handleSave"
    />

    <!-- Delete Confirmation Modal -->
    <DeleteConfirmationModal
      :show="showDeleteModal"
      :item-type="showEvents ? 'event' : 'service'"
      :item-name="(itemToDelete as Event)?.title || (itemToDelete as Service)?.name"
      :item-description="itemToDelete?.description"
      :loading="deleteLoading"
      @confirm="confirmDelete"
      @cancel="cancelDelete"
    />
  </div>
</template>

<script setup lang="ts">
import type { Service, ServiceFormData, Event, EventFormData } from '~/types/service.types'
import { $toast } from '~/composables/useToast'
import { serviceApi, eventApi, filterServices, filterEvents, handleApiError } from '~/utils/services'
import ServiceModal from '~/components/services/ServiceModal.vue'
import ServiceCard from '~/components/services/ServiceCard.vue'
import EventModal from '~/components/events/EventModal.vue'
import EventCard from '~/components/events/EventCard.vue'
import DeleteConfirmationModal from '~/components/ui/DeleteConfirmationModal.vue'

definePageMeta({ layout: 'dashboard' })
useHead({
  title: 'Services & Events - Bookiime',
  meta: [{ name: 'description', content: 'Manage your services, events and offerings' }]
})

// State
const services = ref<Service[]>([])
const events = ref<Event[]>([])
const loading = ref(false)
const showEvents = ref(false)
const searchQuery = ref('')
const showAddModal = ref(false)
const editingItem = ref<Service | Event | null>(null)
const showDeleteModal = ref(false)
const itemToDelete = ref<Service | Event | null>(null)
const deleteLoading = ref(false)

// Computed properties
const currentItems = computed(() => showEvents.value ? filterEvents(events.value, searchQuery.value) : filterServices(services.value, searchQuery.value))
const emptyStateIcon = computed(() => showEvents.value ? 'lucide:calendar' : 'lucide:package')
const emptyStateTitle = computed(() => currentItems.value.length === 0 && searchQuery.value ? `No ${showEvents.value ? 'events' : 'services'} match your search` : `No ${showEvents.value ? 'events' : 'services'} found`)
const emptyStateMessage = computed(() => currentItems.value.length === 0 && searchQuery.value ? 'Try adjusting your search terms.' : `Get started by adding your first ${showEvents.value ? 'event' : 'service'}.`)

// Methods
const toggleButtonClass = (isEvents: boolean) => [
  'flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200',
  showEvents.value === isEvents ? 'bg-white text-primary-600 shadow-sm' : 'text-neutral-600 hover:text-neutral-900'
]

const loadData = async () => {
  loading.value = true
  try {
    const [servicesData] = await Promise.all([
      serviceApi.getTenant(),
      eventApi.getAll().then(data => events.value = data)
    ])
    services.value = servicesData
  } catch (err) {
    $toast(handleApiError(err, 'Failed to load data'), { type: 'error' })
  } finally {
    loading.value = false
  }
}

const editItem = (item: Service | Event) => {
  editingItem.value = { ...item }
}

const handleDeleteClick = (item: Service | Event) => {
  itemToDelete.value = item
  showDeleteModal.value = true
}

const confirmDelete = async () => {
  if (!itemToDelete.value) return

  deleteLoading.value = true
  try {
    if (showEvents.value) {
      await eventApi.delete(itemToDelete.value.id)
      events.value = events.value.filter(e => e.id !== itemToDelete.value!.id)
    } else {
      await serviceApi.delete(itemToDelete.value.id)
      services.value = services.value.filter(s => s.id !== itemToDelete.value!.id)
    }
    $toast(`${showEvents.value ? 'Event' : 'Service'} deleted successfully`, { type: 'success' })
    cancelDelete()
  } catch (err) {
    $toast(handleApiError(err, `Failed to delete ${showEvents.value ? 'event' : 'service'}`), { type: 'error' })
  } finally {
    deleteLoading.value = false
  }
}

const cancelDelete = () => {
  showDeleteModal.value = false
  itemToDelete.value = null
  deleteLoading.value = false
}

const handleSave = async (data: ServiceFormData | EventFormData, imageFile?: File | null) => {
  try {
    if (showEvents.value) {
      const eventData = data as EventFormData
      if (editingItem.value) {
        const updated = await eventApi.update(editingItem.value.id, eventData, imageFile || undefined)
        const index = events.value.findIndex(e => e.id === editingItem.value!.id)
        if (index !== -1) events.value[index] = updated
      } else {
        const newEvent = await eventApi.create(eventData, imageFile || undefined)
        events.value.push(newEvent)
      }
    } else {
      const serviceData = data as ServiceFormData
      if (editingItem.value) {
        const updated = await serviceApi.update(editingItem.value.id, serviceData, imageFile || undefined)
        const index = services.value.findIndex(s => s.id === editingItem.value!.id)
        if (index !== -1) services.value[index] = updated
      } else {
        const newService = await serviceApi.create(serviceData, imageFile || undefined)
        services.value.push(newService)
      }
    }
    $toast(`${showEvents.value ? 'Event' : 'Service'} ${editingItem.value ? 'updated' : 'created'} successfully`, { type: 'success' })
    closeModal()
  } catch (err) {
    $toast(handleApiError(err, `Failed to save ${showEvents.value ? 'event' : 'service'}`), { type: 'error' })
  }
}

const closeModal = () => {
  showAddModal.value = false
  editingItem.value = null
}

onMounted(loadData)

</script>