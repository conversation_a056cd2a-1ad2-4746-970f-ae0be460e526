import type { Service, ServiceFormData, Event, EventFormData } from '~/types/service.types'

// Currency formatting for Ghana Cedis
export const formatCurrency = (amount: number): string => {
  return `₵${amount.toFixed(2)}`
}

// Duration formatting
export const formatDuration = (minutes: number): string => {
  if (minutes < 60) {
    return `${minutes}min`
  }
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}min` : `${hours}h`
}

// API error handling
export const handleApiError = (error: any, defaultMessage: string): string => {
  if (error?.data?.message) {
    return error.data.message
  }
  if (error?.message) {
    return error.message
  }
  return defaultMessage
}

// Service API functions
export const serviceApi = {
  async getAll(): Promise<Service[]> {
    const config = useRuntimeConfig()
    return await $fetch<Service[]>(`${config.public.apiBase}/services`)
  },

  async getTenant(): Promise<Service[]> {
    const config = useRuntimeConfig()
    const { $auth } = useNuxtApp()
    return await $fetch<Service[]>(`${config.public.apiBase}/services/tenant`, {
      headers: {
        'Authorization': `Bearer ${$auth.value.token}`,
        'Accept': 'application/json'
      }
    })
  },

  async getById(id: string): Promise<Service> {
    const config = useRuntimeConfig()
    const { $auth } = useNuxtApp()
    return await $fetch<Service>(`${config.public.apiBase}/services/${id}`, {
      headers: {
        'Authorization': `Bearer ${$auth.value.token}`,
        'Accept': 'application/json'
      }
    })
  },

  async create(data: ServiceFormData, imageFile?: File): Promise<Service> {
    const formData = new FormData()
    formData.append('name', data.name)
    formData.append('price', data.price.toString())

    if (data.description) formData.append('description', data.description)
    if (data.categoryId) formData.append('categoryId', data.categoryId)
    if (imageFile) formData.append('image', imageFile)

    return await $apiFetch<Service>('/services', formData)
  },

  async update(id: string, data: Partial<ServiceFormData>, imageFile?: File): Promise<Service> {
    const formData = new FormData()

    if (data.name !== undefined) formData.append('name', data.name)
    if (data.price !== undefined) formData.append('price', data.price.toString())
    if (data.description !== undefined) formData.append('description', data.description)
    if (data.categoryId !== undefined) formData.append('categoryId', data.categoryId)
    if (imageFile) formData.append('image', imageFile)

    const config = useRuntimeConfig()
    const { $auth } = useNuxtApp()
    return await $fetch<Service>(`${config.public.apiBase}/services/${id}`, {
      method: 'PUT',
      body: formData,
      headers: {
        'Authorization': `Bearer ${$auth.value.token}`
      }
    })
  },

  async delete(id: string): Promise<void> {
    const config = useRuntimeConfig()
    const { $auth } = useNuxtApp()
    await $fetch(`${config.public.apiBase}/services/${id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${$auth.value.token}`,
        'Accept': 'application/json'
      }
    })
  }
}

// Event management functions (basic implementation)
export const eventApi = {
  async getAll(): Promise<Event[]> {
    // Mock implementation - replace with actual API when available
    return []
  },

  async create(data: EventFormData, imageFile?: File): Promise<Event> {
    // Mock implementation - replace with actual API when available
    const newEvent: Event = {
      id: Date.now().toString(),
      ...data,
      currentAttendees: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    // If imageFile is provided, you would upload it here and set the imageUrl
    if (imageFile) {
      // Mock image URL - in real implementation, upload the file and get the URL
      newEvent.imageUrl = URL.createObjectURL(imageFile)
    }

    return newEvent
  },

  async update(id: string, data: EventFormData, imageFile?: File): Promise<Event> {
    // Mock implementation - replace with actual API when available
    const updatedEvent: Event = {
      id,
      ...data,
      currentAttendees: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    // If imageFile is provided, you would upload it here and set the imageUrl
    if (imageFile) {
      // Mock image URL - in real implementation, upload the file and get the URL
      updatedEvent.imageUrl = URL.createObjectURL(imageFile)
    }

    return updatedEvent
  },

  async delete(id: string): Promise<void> {
    // Mock implementation - replace with actual API when available
    console.log('Deleting event:', id)
  }
}

// Search and filter utilities
export const filterServices = (services: Service[], query: string): Service[] => {
  if (!query.trim()) return services
  const searchTerm = query.toLowerCase()
  return services.filter(service => 
    service.name.toLowerCase().includes(searchTerm) ||
    service.description.toLowerCase().includes(searchTerm) ||
    service.category?.toLowerCase().includes(searchTerm)
  )
}

export const filterEvents = (events: Event[], query: string): Event[] => {
  if (!query.trim()) return events
  const searchTerm = query.toLowerCase()
  return events.filter(event => 
    event.title.toLowerCase().includes(searchTerm) ||
    event.description.toLowerCase().includes(searchTerm) ||
    event.location?.toLowerCase().includes(searchTerm) ||
    event.category?.toLowerCase().includes(searchTerm)
  )
}
